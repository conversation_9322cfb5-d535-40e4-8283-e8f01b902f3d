"""tools for agents"""

import asyncio
import inspect
import json
from abc import ABC, abstractmethod
from typing import Protocol, Any, Callable, Dict, List, Optional, AsyncGenerator, Iterator

from pydantic import BaseModel, Field

from pycommon.logwrapper import get_log_wrapper
from pylibs.pytable import SamTable
from .base import LlmAgent, LlmAgentConfig, AgentResult

logger = get_log_wrapper(__name__)


class InvokeAble(Protocol):
    """interface for invokable objects"""

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """invoke the object"""

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """async invoke the object"""


class DescribeAble(Protocol):
    """interface for objects that can be described"""

    def describe(self) -> str:
        """describe the object"""


class CategorizeAble(Protocol):
    """interface for objects that can be categorized"""

    def get_categories(self) -> set[str]:
        """return categories of the object"""


class Tool(InvokeAble, DescribeAble, CategorizeAble):
    """Tool base"""

    name: str
    description: str
    categories: set[str]

    def as_prompt(self) -> str:
        """return tool as a LLM prompt"""
        return f"{self.name}: {self.description}"

    @abstractmethod
    def as_schema(self) -> dict[str, Any]:
        """return tool's schema as a LLM prompt"""


class AgentTool(Tool, ABC):
    """Tool for LLM agent"""

    def __init__(self, name: str, description: str, categories: set[str] | None = None, **kwargs: Any):
        self.name = name
        self.description = description
        self.categories = categories or set()

    def describe(self) -> str:
        return self.description

    def get_categories(self) -> set[str]:
        return self.categories

    def as_schema(self) -> dict[str, Any]:
        """Convert tool to LLM function calling format"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.get_parameters_schema(),
            },
        }

    @abstractmethod
    def get_parameters_schema(self) -> dict[str, Any]:
        """Get JSON schema for tool parameters"""


class LocalAgentTool(AgentTool):
    """Local agent tool implementation"""

    def __init__(self, name: str, description: str, categories: set[str] | None = None, **kwargs):
        super().__init__(name, description, categories)
        self.func: Callable = kwargs.get("func")
        self.is_async = inspect.iscoroutinefunction(self.func)
        self._parameters_schema = self._generate_schema()

    def _generate_schema(self) -> dict[str, Any]:
        """Generate JSON schema from function signature"""
        sig = inspect.signature(self.func)
        parameters = {"type": "object", "properties": {}, "required": []}

        for param_name, param in sig.parameters.items():
            param_type = "string"  # Default type
            if param.annotation != inspect.Parameter.empty:
                if param.annotation == str:
                    param_type = "string"
                elif param.annotation == int:
                    param_type = "integer"
                elif param.annotation == float:
                    param_type = "number"
                elif param.annotation == bool:
                    param_type = "boolean"
                elif param.annotation == list or str(param.annotation).startswith("typing.List"):
                    param_type = "array"
                elif param.annotation == dict or str(param.annotation).startswith("typing.Dict"):
                    param_type = "object"

            parameters["properties"][param_name] = {"type": param_type}

            if param.default == inspect.Parameter.empty:
                parameters["required"].append(param_name)

        return parameters

    def get_parameters_schema(self) -> dict[str, Any]:
        return self._parameters_schema

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Synchronous invoke"""
        if self.is_async:
            raise ValueError(f"Tool {self.name} is async, use async_invoke instead")
        return self.func(*args, **kwargs)

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Asynchronous invoke"""
        if self.is_async:
            return await self.func(*args, **kwargs)
        else:
            return await asyncio.to_thread(self.func, *args, **kwargs)


class McpAgentTool(AgentTool):
    """MCP agent tool implementation"""

    def __init__(
        self,
        name: str,
        description: str,
        categories: set[str] | None = None,
        **kwargs: Any,
    ):
        super().__init__(name, description, categories)
        self.conf = kwargs.get("conf", {})  # MCP tool config
        # Initialize parameters schema from MCP tool config
        self._parameters_schema = self.conf.get("parameters_schema", {
            "type": "object",
            "properties": {},
            "required": []
        })

    def get_parameters_schema(self) -> Dict[str, Any]:
        return self._parameters_schema

    def invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Invoke MCP tool - to be implemented"""
        raise NotImplementedError("MCP tool invocation not implemented yet")

    async def async_invoke(self, *args: Any, **kwargs: Any) -> Any:
        """Async invoke MCP tool - to be implemented"""
        raise NotImplementedError("Async MCP tool invocation not implemented yet")


class ToolRepo(Protocol):
    """interface for a tool repository"""

    def load(self, definition: str | dict) -> None:
        """load repo definitions from source.
        Source depends on different Repo types.
        """

    def list(self, page: int = 0, page_size: int = 100, filters: str | None = None) -> list[Tool]:
        """list all tools"""

    def register(self, name: str, desc: str = "", categories: set[str] | None = None) -> bool:
        """register the tool to agent"""

    def unregister(self, name: str) -> None:
        """unregister the tool from agent"""

    def clear(self) -> None:
        """clear all tools in repo"""

    def as_prompt(self) -> str:
        """return tool list as a LLM prompt"""

    def __getitem__(self, name: str) -> Tool:
        """get tool by name"""

    def __contains__(self, name: str) -> bool:
        """check if tool exists"""

    def __iter__(self) -> Iterator[Tool]:
        """iterate all tools"""


class BaseToolRepo(ToolRepo, ABC):

    def __init__(self, **kwargs: Any):
        pass

    def as_prompt(self) -> str:
        """return tool list as a LLM prompt"""
        tools = self.list()
        tool_prompts = [f"{t.name}: {t.description}" for t in tools]
        return "\n".join(tool_prompts)


class LocalToolRepo(ToolRepo):
    """local tool repository"""

    def __init__(self, **kwargs: Any) -> None:
        self._tools_table = SamTable(columns=["name", "description", "categories", "tool"])
        self._tools: Dict[str, LocalAgentTool] = {}

    def load(self, definition: str | dict) -> None:
        """Load tool definitions from file or dict"""
        if isinstance(definition, str):
            # Load from file path
            import json
            try:
                with open(definition, 'r', encoding='utf-8') as f:
                    tool_defs = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.error(f"Failed to load tool definitions from {definition}: {e}")
                return
        elif isinstance(definition, dict):
            tool_defs = definition
        else:
            logger.error(f"Invalid definition type: {type(definition)}")
            return

        # Load each tool definition
        for tool_def in tool_defs.get('tools', []):
            name = tool_def.get('name')
            desc = tool_def.get('description', '')
            categories = set(tool_def.get('categories', []))

            if not name:
                logger.warning("Tool definition missing name, skipping")
                continue

            # Check if name already exists
            if name in self._tools:
                logger.warning(f"Tool '{name}' already exists, skipping")
                continue

            # For local tools, we need a function reference which can't be loaded from JSON
            # This is mainly for configuration-based tool loading
            logger.info(f"Loaded tool definition: {name} (function needs to be registered separately)")

    def list(self, page: int = 0, page_size: int = 100, filters: str | None = None) -> list[LocalAgentTool]:
        """List all tools with pagination"""
        tools = list(self._tools.values())

        if filters:
            # Simple filter by name or description
            tools = [t for t in tools if filters.lower() in t.name.lower() or filters.lower() in t.description.lower()]

        start = page * page_size
        end = start + page_size
        return tools[start:end]

    def register(
        self, name: str, desc: str = "", categories: set[str] | None = None, func: Callable | None = None
    ) -> bool:
        """Register a tool"""
        if func is None:
            return False

        # Check if name already exists
        if name in self._tools:
            logger.warning(f"Tool '{name}' already exists, registration failed")
            return False

        tool = LocalAgentTool(name, desc, categories=categories, func=func)
        self._tools[name] = tool

        # Add to table for tracking
        self._tools_table.add_row({"name": name, "description": desc, "categories": categories or set(), "tool": tool})

        logger.debug(f"Registered tool: {name}")
        return True

    def unregister(self, name: str) -> None:
        """Unregister a tool"""
        if name in self._tools:
            del self._tools[name]
            # Remove from table
            self._tools_table.delete_rows(lambda row: row["name"] == name)
            logger.debug(f"Unregistered tool: {name}")

    def clear(self) -> None:
        """Clear all tools in repo"""
        self._tools.clear()
        self._tools_table.clear()
        logger.debug("Cleared all tools from local repository")

    def __getitem__(self, name: str) -> LocalAgentTool:
        """Get tool by name"""
        if name not in self._tools:
            raise KeyError(f"Tool '{name}' not found")
        return self._tools[name]

    def __contains__(self, name: str) -> bool:
        """Check if tool exists"""
        return name in self._tools

    def __iter__(self) -> Iterator[LocalAgentTool]:
        """Iterate all tools"""
        return iter(self._tools.values())

    def get_tool(self, name: str) -> LocalAgentTool | None:
        """Get a specific tool by name"""
        return self._tools.get(name)


class McpToolRepo(ToolRepo):
    """MCP tool repository"""

    def __init__(self):
        self._cached_tools: Dict[str, McpAgentTool] = {}
        self._mcp_server_config: Dict[str, Any] = {}

    def load(self, definition: str | dict) -> None:
        """Load MCP server configuration and tools"""
        if isinstance(definition, str):
            # Load from file path
            import json
            try:
                with open(definition, 'r', encoding='utf-8') as f:
                    mcp_config = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.error(f"Failed to load MCP server config from {definition}: {e}")
                return
        elif isinstance(definition, dict):
            mcp_config = definition
        else:
            logger.error(f"Invalid definition type: {type(definition)}")
            return

        # Store MCP server configuration
        self._mcp_server_config = mcp_config

        # Load tools from MCP server
        self._load_mcp_tools_from_config(mcp_config)

    def _load_mcp_tools_from_config(self, config: Dict[str, Any]) -> None:
        """Load and cache MCP tools from MCP server configuration"""
        # TODO: Implement MCP client integration
        # This should:
        # 1. Connect to MCP server using config (host, port, auth, etc.)
        # 2. Call MCP list_tools() to get available tools
        # 3. Cache the tools with name existence checking

        server_url = config.get('server_url', '')
        tools_config = config.get('tools', [])

        logger.info(f"Loading MCP tools from server: {server_url}")

        for tool_config in tools_config:
            name = tool_config.get('name')
            desc = tool_config.get('description', '')
            categories = set(tool_config.get('categories', []))

            if not name:
                logger.warning("MCP tool config missing name, skipping")
                continue

            # Check if name already exists
            if name in self._cached_tools:
                logger.warning(f"MCP tool '{name}' already exists, skipping")
                continue

            # Create MCP tool instance
            tool = McpAgentTool(name, desc, categories, conf=tool_config)
            self._cached_tools[name] = tool
            logger.debug(f"Loaded MCP tool: {name}")

    def list(self, page: int = 0, page_size: int = 100, filters: str | None = None) -> list[McpAgentTool]:
        """List MCP tools"""
        tools = list(self._cached_tools.values())

        if filters:
            tools = [t for t in tools if filters.lower() in t.name.lower() or filters.lower() in t.description.lower()]

        start = page * page_size
        end = start + page_size
        return tools[start:end]

    def register(self, name: str, desc: str = "", categories: set[str] | None = None) -> bool:
        """MCP tools are loaded from server, not registered manually"""
        logger.warning("MCP tools are loaded from server configuration, not registered manually")
        return False

    def unregister(self, name: str) -> None:
        """MCP tools are managed by server, not unregistered manually"""
        logger.warning("MCP tools are managed by server, not unregistered manually")

    def clear(self) -> None:
        """Clear all cached MCP tools"""
        self._cached_tools.clear()
        logger.debug("Cleared all cached MCP tools")

    def __getitem__(self, name: str) -> McpAgentTool:
        """Get MCP tool by name"""
        if name not in self._cached_tools:
            raise KeyError(f"MCP tool '{name}' not found")
        return self._cached_tools[name]

    def __contains__(self, name: str) -> bool:
        """Check if MCP tool exists"""
        return name in self._cached_tools

    def __iter__(self) -> Iterator[McpAgentTool]:
        """Iterate all MCP tools"""
        return iter(self._cached_tools.values())

    def get_tool(self, name: str) -> McpAgentTool | None:
        """Get a specific MCP tool by name"""
        return self._cached_tools.get(name)


# Global repositories
_local_repo = LocalToolRepo()
_mcp_repo = McpToolRepo()


def agent_tool(
    repo: ToolRepo = _local_repo,
    name: Optional[str] = None,
    description: Optional[str] = None,
    categories: set[str] | None = None,
):
    """Decorator to register a function as an agent tool.

    Args:
        repo: Tool repository to register the tool. Default is local repository.
        name: Tool name. If None, uses function name.
        description: Tool description. If None, uses function docstring.
        categories: Tool categories for organization.

    Example:
        @agent_tool(my_tool_repo)
        def get_weather(city: str) -> str:
            return f"Weather in {city}: sunny, 25°C"

        @agent_tool(description="Get current weather")
        def get_weather(city: str) -> str:
            return f"Weather in {city}: sunny, 25°C"

        @agent_tool(categories={"search", "web"})
        async def async_search(query: str) -> List[str]:
            '''Search for information'''
            return ["result1", "result2"]
    """

    def decorator(func: Callable) -> Callable:
        tool_name = name or func.__name__
        tool_desc = description or (func.__doc__ or "").strip()

        # Register the tool in local repository
        repo.register(name=tool_name, desc=tool_desc, categories=categories, func=func)

        return func

    return decorator


class ToolCallResult(BaseModel):
    """Result of a tool call."""

    tool_name: str
    success: bool
    result: Any = None
    error: Optional[str] = None


class ToolAgentConfig(LlmAgentConfig):
    """Configuration for ToolAgent."""

    name: str = Field(default="ToolAgent")
    max_tool_calls: int = Field(default=10)
    tool_call_timeout: int = Field(default=30)
    available_tools: Optional[List[str]] = Field(default=None)
    use_local_tools: bool = Field(default=True)
    use_mcp_tools: bool = Field(default=True)


class ToolAgent(LlmAgent):
    """Agent with tool calling capabilities using the tool repository system."""

    config: ToolAgentConfig

    def __init__(self, config: Optional[ToolAgentConfig] = None, **kwargs):
        super().__init__(config, **kwargs)
        self.local_repo = _local_repo
        self.mcp_repo = _mcp_repo

    def _get_available_tools(self) -> Dict[str, AgentTool]:
        """Get all available tools from repositories"""
        tools = {}

        # Add local tools
        if self.config.use_local_tools:
            for tool in self.local_repo.list():
                if self.config.available_tools is None or tool.name in self.config.available_tools:
                    tools[tool.name] = tool

        # Add MCP tools
        if self.config.use_mcp_tools:
            for tool in self.mcp_repo.list():
                if self.config.available_tools is None or tool.name in self.config.available_tools:
                    tools[tool.name] = tool

        return tools

    def _get_tools_for_llm(self) -> List[Dict[str, Any]]:
        """Format tools for LLM function calling"""
        tools = self._get_available_tools()
        return [tool.as_schema() for tool in tools.values()]

    async def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolCallResult:
        """Execute a tool call safely"""
        available_tools = self._get_available_tools()

        if tool_name not in available_tools:
            return ToolCallResult(tool_name=tool_name, success=False, error=f"Tool '{tool_name}' not found")

        tool = available_tools[tool_name]

        try:
            if isinstance(tool, LocalAgentTool):
                result = await asyncio.wait_for(tool.async_invoke(**arguments), timeout=self.config.tool_call_timeout)
            elif isinstance(tool, McpAgentTool):
                result = await asyncio.wait_for(tool.async_invoke(**arguments), timeout=self.config.tool_call_timeout)
            else:
                result = await asyncio.wait_for(
                    asyncio.to_thread(tool.invoke, **arguments), timeout=self.config.tool_call_timeout
                )

            return ToolCallResult(tool_name=tool_name, success=True, result=result)

        except asyncio.TimeoutError:
            return ToolCallResult(
                tool_name=tool_name,
                success=False,
                error=f"Tool execution timed out after {self.config.tool_call_timeout} seconds",
            )
        except Exception as e:
            return ToolCallResult(tool_name=tool_name, success=False, error=f"Tool execution failed: {str(e)}")

    async def _async_run(self, user_query: str, **kwargs: Any) -> AgentResult:
        """Run the agent with tool calling capability"""
        tools = self._get_tools_for_llm()

        if not tools:
            return await self.async_invoke_llm(user_query, resp_type="text")

        system_prompt = (
            "You are an AI assistant with access to tools. "
            "Use the available tools to help answer the user's question. "
            "Call tools when needed and provide a comprehensive response based on the results."
        )

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_query}]

        tool_calls_count = 0
        max_calls = self.config.max_tool_calls

        while tool_calls_count < max_calls:
            response = await self.async_invoke_llm(prompt=messages, resp_type="object")

            if not isinstance(response, dict) or "tool_calls" not in response:
                return response

            tool_calls = response["tool_calls"]
            if not tool_calls:
                return response

            messages.append({"role": "assistant", "content": response.get("content", ""), "tool_calls": tool_calls})

            for tool_call in tool_calls:
                tool_calls_count += 1

                if tool_calls_count >= max_calls:
                    logger.warning(f"Reached maximum tool calls limit: {max_calls}")
                    break

                function = tool_call.get("function", {})
                tool_name = function.get("name")
                arguments = function.get("arguments", {})

                if isinstance(arguments, str):
                    try:
                        arguments = json.loads(arguments)
                    except json.JSONDecodeError:
                        arguments = {}

                result = await self._execute_tool(tool_name, arguments)

                tool_result = {
                    "role": "tool",
                    "tool_call_id": tool_call.get("id", f"call_{tool_calls_count}"),
                    "name": tool_name,
                    "content": json.dumps({"success": result.success, "result": result.result, "error": result.error}),
                }
                messages.append(tool_result)

                self.ob.carry(
                    "tool_calls", {"tool_name": tool_name, "arguments": arguments, "result": result.model_dump()}
                )

            if tool_calls_count >= max_calls:
                break

        final_response = await self.async_invoke_llm(
            prompt=messages
            + [{"role": "user", "content": "Please provide a final response based on the tool results above."}],
            resp_type="text",
        )

        return final_response

    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        """Iterate agent results - not implemented for tool agent"""
        raise NotImplementedError("Streaming not supported for ToolAgent yet")


# Convenience functions
def get_local_tools() -> List[LocalAgentTool]:
    """Get all registered local tools"""
    return _local_repo.list()


def get_mcp_tools() -> List[McpAgentTool]:
    """Get all cached MCP tools"""
    return _mcp_repo.list()


def clear_local_tools():
    """Clear all local tools (mainly for testing)"""
    global _local_repo
    _local_repo = LocalToolRepo()
