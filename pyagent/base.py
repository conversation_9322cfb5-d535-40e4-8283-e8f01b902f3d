"""Protocols/interfaces, base abstracted classes, base models and etc for the GBS Agent module."""  # noqa: E501

import json
import os
import re
import time
import traceback
from abc import ABC, abstractmethod
from typing import Any, Literal, Protocol, AsyncGenerator, get_type_hints, Self

import json_repair
import litellm

from .tool import ToolRepo

# fmt: off
litellm.log_raw_request_response = False
litellm.suppress_debug_info = True
litellm.turn_off_message_logging = True
litellm.logging = False

# fmt: on

from asgiref.sync import sync_to_async, async_to_sync
from pydantic import BaseModel, Field

from pycommon.logwrapper import get_log_wrapper, LOG_SDK
from pycommon.observation import ObserverManager, Observable
from pycommon.protocol_mixin import StopAbleMixin, ProgressAbleMixin
from .consts import LiteLlmModels
from .prompt import PromptMessages

if LOG_SDK == "logfire":
    # Globally init litellm logging with logfile
    litellm.callbacks = ["logfire"]
    # litellm.success_callback = ["logfire"]
    # litellm.failure_callback = ["logfire"]

if os.getenv("LLM_CACHE", "").lower() in {"1", "true", "yes", "on"}:
    # https://docs.litellm.ai/docs/caching/all_caches
    # litellm.cache = litellm.Cache(type="redis", host="127.0.0.1", port=6379, password=<password>)
    litellm.cache = litellm.Cache(type="disk", disk_cache_dir="/tmp/litellm-cache", ttl=3600 * 3)

LLM_RESP_TYPE = Literal["text", "object"] | type[BaseModel]
LlmResponse = str | BaseModel | dict | list | None
AgentResult = Any

logger = get_log_wrapper(__name__)


# =====  Agent Config Abstractions  =====


class ModelMixin(BaseModel):

    def to_dict(self, include: set[str] | None = None, exclude: set[str] | None = None) -> dict:
        return self.model_dump(include=include, exclude=exclude)

    def from_dict(self, d: dict) -> Self:
        return self.model_validate(d)

    @classmethod
    def from_json_str(cls, s: str) -> Self:
        return cls.model_validate_json(s)

    def update(self, d: dict | object | BaseModel) -> None:
        if isinstance(d, BaseModel):
            d = d.to_dict()
        elif isinstance(d, object):
            d = d.__dict__
        for k, v in d.items():
            if k in self.__dict__:
                setattr(self, k, v)

    def as_schema(self) -> dict[str, Any]:
        return self.model_json_schema()


class AgentConfig(ModelMixin, BaseModel, ABC):
    name: str = Field(default="Agent")

    def __str__(self) -> str:
        return f"{self.name}Config"


class LlmAgentConfig(AgentConfig):
    name: str = Field(default="LlmAgent")

    # gpt-4o-mini, o4-mini, claude-sonnet-4-20250514. see https://docs.litellm.ai/docs/providers/openai
    llm_model: str = Field(default=LiteLlmModels.DEFAULT)
    llm_output_tokens: int = Field(default=8192)
    llm_temperature: float = Field(default=1.0)
    llm_timeout: int = Field(default=30)  # seconds

    # whether streaming the result
    streaming: bool = Field(default=False)

    def __str__(self) -> str:
        return f"{self.name}Config ({self.to_dict()})"


# =====. Agent Abstractions  =====


class PyAgentException(Exception):
    pass


class Agent(Protocol):
    config: AgentConfig

    """interface for all agent"""

    @abstractmethod
    def run(self, **kwargs: Any) -> AgentResult:
        """run agent synchronously"""

    @abstractmethod
    async def async_run(self, **kwargs: Any) -> AgentResult:
        """run agent asynchronously"""

    @abstractmethod
    async def async_iter(self, **kwargs: Any) -> AsyncGenerator:
        """iterate agent result asynchronously"""


class BaseAgent(Agent, ABC):
    """abstract base for all agents"""

    config: AgentConfig
    ob: Observable

    def __init__(self, config: AgentConfig | None = None, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        if hasattr(self, "config"):
            config = self.config

        if config is None:
            config_cls = get_type_hints(self.__class__).get("config")

            # config_cls = None
            # for cls in self.__class__.__mro__:
            #     if cls is object:
            #         continue
            #
            #     hints = get_type_hints(cls)
            #     config_cls = hints.get('config')
            #
            #     if config_cls is not None:
            #         logger.debug(f"found config `{config_cls.__name__}` from `{cls.__name__}` for `{self.__class__.__name__}`")
            #         break

            if config_cls is None:
                raise PyAgentException(
                    f"You have no attribute `config: XxxConfig` defined for your agent `{self.__class__.__name__}` or its parents."
                    "I can not create a default config for your agent. Please define it or pass `config` argument in."
                )
            else:
                logger.info(f"found config `{config_cls.__name__}` for `{self.__class__.__name__}`")
                config = config_cls()

        if not isinstance(config, AgentConfig):
            raise ValueError(f"config must be an instance of AgentConfig, but got {type(config)}")

        self.config = config
        self.ob = ObserverManager()

    def _run(self, **kwargs: Any) -> AgentResult:
        """Synchronous implementation that children should implement"""
        return async_to_sync(self._async_run)(**kwargs)

    @abstractmethod
    async def _async_run(self, **kwargs: Any) -> AgentResult:
        """Asynchronous implementation that children should implement"""

    @abstractmethod
    async def _async_iter(self, **kwargs: Any) -> AsyncGenerator:
        """iterate agent result asynchronously that children should implement"""

    def run(self, **kwargs: Any) -> AgentResult:
        if self.config.streaming:
            raise NotImplementedError("streaming is not supported yet.")

        result = None
        try:
            self.ob.carry("input", kwargs)
            result = self._run(**kwargs)
            self.ob.carry("output", result)
        finally:
            try:
                self._save_state(**kwargs)
            except Exception as e:
                logger.error(f"Fail to save state: {e}")

        return result

    async def async_run(self, **kwargs: Any) -> AgentResult:
        if self.config.streaming:
            raise NotImplementedError("streaming is not supported yet.")

        result = None
        try:
            self.ob.carry("input", kwargs)
            result = await self._async_run(**kwargs)
            self.ob.carry("output", result)
        except Exception as e:
            logger.exception(e)
            self.ob.update("errors", {str(e): traceback.format_exception(e)})
        finally:
            try:
                await self._async_save_state(**kwargs)
            except Exception as e:
                logger.error(f"Fail to save state: {e}")

        return result

    async def async_iter(self, **kwargs: Any) -> AsyncGenerator:
        if self.config.streaming:
            raise NotImplementedError("streaming is not supported yet.")

        try:
            async for o in await self._async_iter(**kwargs):
                yield o
        finally:
            try:
                await self._async_save_state(**kwargs)
            except Exception as e:
                logger.error(f"Fail to save state: {e}")

    @abstractmethod
    def _save_state(self, **kwargs: Any) -> None:
        """to save agent state."""

    @abstractmethod
    async def _async_save_state(self, **kwargs: Any) -> None:
        """to save agent state asynchronously."""


class LlmAgent(BaseAgent, StopAbleMixin, ProgressAbleMixin, ABC):
    """Abstract base for all LLM Agents"""

    config: LlmAgentConfig
    tools: ToolRepo

    def invoke_llm(
        self,
        prompt: str | PromptMessages,
        streaming: bool = False,
        resp_type: LLM_RESP_TYPE = "object",
        raise_if_error: bool = False,
    ) -> LlmResponse:
        """invoke llm synchronously

        Args:
            prompt: prompt to invoke llm
            streaming: whether streaming result
            resp_type: type of response, can be "text", "object", model (a class of type[pydantic.BaseModel], e.g. MyModel, and MyModel is subclass of BaseModel)
                - "text": return raw string response
                - "object": return Python dict or list object by JSON loaded.
                - model: return parsed model object (instance of sub-class of `pydantic.BaseModel`)
                - None: error or no response.
            raise_if_error: whether raise it if there is an error occurs
        """
        if streaming:
            raise NotImplementedError("streaming is not supported yet.")

        resp: LlmResponse = None
        exc = None
        try:
            # set callback
            litellm.success_callback = [self._litellm_callback]  # set custom callback function
            # litellm.input_callback=["sentry"] # for sentry breadcrumbing - logs the input being sent to the api
            # litellm.success_callback=["posthog", "helicone", "langfuse", "lunary", "athina"]
            # litellm.failure_callback=["sentry", "lunary", "langfuse"]

            if isinstance(prompt, str):
                messages = [{"content": prompt, "role": "user"}]
            else:
                messages = prompt

            prompt_log_msg = "".join([m["content"] for m in messages]) if isinstance(messages, list) else messages
            logger.debug(f"LLM Prompt: {prompt_log_msg}")
            self.ob.carry("llm_prompts", messages)

            ts = time.perf_counter()
            litellm_resp: litellm.ModelResponse | litellm.CustomStreamWrapper = litellm.completion(
                model=self.config.llm_model,
                messages=messages,
                temperature=self.config.llm_temperature,
                max_completion_tokens=self.config.llm_output_tokens,
                timeout=self.config.llm_timeout,
                stream=streaming,
                response_format={"type": "json_object"} if resp_type in {"object", type(BaseModel)} else None,
                caching=True if litellm.cache else False,
                drop_params=True,  # 非openai模型丢弃不支持的参数
            )
            ts = time.perf_counter() - ts
            logger.debug(f"LLM took {ts:.4f} seconds.")
            self.ob.update("perf", {"invoke_llm": ts})

            resp_text = self._handle_output(litellm_resp)

            if isinstance(resp_type, type) and issubclass(resp_type, BaseModel):
                resp = resp_type.model_validate_json(resp_text)
            elif resp_type == "object":
                resp = json_repair.loads(resp_text)
                if len(resp_text) >= self.config.llm_output_tokens * 0.95:
                    setattr(resp, "__maybe_truncated__", True)  # type: ignore
            elif resp_type == "text":
                resp = resp_text
            else:
                raise ValueError(f"Unsupported resp_type: {resp_type}")
        except Exception as e:
            exc = e
            logger.error(f"Failed to invoke LLM: {e}")
            e_stack_str = "".join(traceback.format_exception(e))
            self.ob.update("errors", {str(e): traceback.format_exception(e)})
            if not raise_if_error:
                logger.debug(e_stack_str)
        finally:
            # self.ob.carry("output", {"invoke_llm": resp})
            pass

        if exc is not None and raise_if_error:
            raise exc
        return resp

    async def async_invoke_llm(
        self,
        prompt: str | PromptMessages,
        streaming: bool = False,
        resp_type: LLM_RESP_TYPE = "text",
        raise_if_error: bool = True,
    ) -> LlmResponse:
        """invoke llm asynchronously"""
        # warnings.warn("Async version to be implemented. Temporarily use `sync_to_async` to call `invoke_llm`.",
        #               UserWarning)
        return await sync_to_async(self.invoke_llm)(
            prompt=prompt, streaming=streaming, resp_type=resp_type, raise_if_error=raise_if_error
        )

    def _litellm_callback(
        self,
        kwargs,  # kwargs to completion
        completion_response,  # response from completion
        start_time,
        end_time,  # start/end time
    ):
        try:
            # response_cost = kwargs.get('_hidden_params', {}).get("response_cost", 0)
            # print("response_cost", response_cost)
            params_to_model = kwargs["additional_args"]["complete_input_dict"]
            logger.debug(f"params to model: {json.dumps(params_to_model,indent=2, default=str, ensure_ascii=False)}")
            pass
        except Exception as e:
            logger.warning(f"Error litellm callback. {e}")

    def _handle_output(
        self, litellm_resp: litellm.ModelResponse | litellm.CustomStreamWrapper
    ) -> str | litellm.CustomStreamWrapper:
        if isinstance(litellm_resp, litellm.CustomStreamWrapper):
            return litellm_resp

        resp = litellm_resp.choices[0].message.content
        self.ob.carry("llm_output", litellm_resp.to_dict())
        logger.debug(f"LLM output: {resp}")

        try:
            # cost: https://docs.litellm.ai/docs/completion/token_usage
            cost = {
                "input_tokens": litellm_resp.usage.prompt_tokens,
                "output_tokens": litellm_resp.usage.completion_tokens,
                "cache_hit": litellm_resp._hidden_params.get("cache_hit", False),
                "cost": litellm_resp._hidden_params.get("response_cost", -1),
            }
            self.ob.carry("llm_cost", cost)
            logger.warning(f"llm_cost: {cost['cost']:.4f} | cache hit: {cost['cache_hit']}")
        except Exception as e:
            logger.error(f"Fail to get cost. {e}")

        if not resp or (isinstance(resp, str) and resp.strip() in {"{}", "[]"}):
            logger.error(f"LLM got empty output '{resp}'. Full response: {litellm_resp.model_dump_json(indent=2)}")
            raise PyAgentException(f"LLM Error: empty output")

        if len(resp) > self.config.llm_output_tokens * 0.95:
            logger.error(
                f"LLM output too long. Response may not complete. {len(resp)} (limit {self.config.token_limit})"
            )

        resp = re.sub(r"^```\w*\n", "", resp)
        resp = re.sub(r"```$", "", resp)
        return resp

    def _save_state(self, **kwargs: Any) -> None:
        logger.debug(f"saving agent stats ({self.config.name})")

    async def _async_save_state(self, **kwargs: Any) -> None:
        await sync_to_async(self._save_state)(**kwargs)
